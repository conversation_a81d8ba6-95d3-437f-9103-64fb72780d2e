msgid ""
msgstr ""
"Project-Id-Version: FlagShip for WooCommerce 2.0.0\n"
"Report-Msgid-Bugs-To: https://github.com/flagshipcompany/flagship-for-woocommerce/issues\n"
"POT-Creation-Date: 2017-04-19 14:57-0400\n"
"PO-Revision-Date: 2021-04-14 13:10-0400\n"
"Last-Translator: (<PERSON>) <PERSON> <<EMAIL>>\n"
"Language-Team: (<PERSON>) <PERSON> <<EMAIL>>\n"
"Language: fr_FR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-KeywordsList: ;__;_e;esc_attr_e;error;notice;warning;sprintf\n"
"X-Poedit-Basepath: ..\n"
"X-Generator: Poedit 2.4.2\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: node_modules\n"
"X-Poedit-SearchPathExcluded-1: vendor\n"
"X-Poedit-SearchPathExcluded-2: tests\n"

#: src/Components/Event/Listener/MetaboxDisplay.php:47
msgid "FlagShip"
msgstr "FlagShip"

#: src/Components/Event/Listener/PickupPostType.php:47
msgid "Pick-ups"
msgstr "Ramassages"

#: src/Components/Event/Listener/PickupPostType.php:48 templates/meta-boxes/order-flagship-shipping-actions.php:45
msgid "Pick-up"
msgstr "Ramassage"

#: src/Components/Event/Listener/PickupPostType.php:50
msgid "Add Pick-up"
msgstr "+ Ramassage"

#: src/Components/Event/Listener/PickupPostType.php:51
msgid "Schedule New Pick-up"
msgstr "Nouveau ramassage"

#: src/Components/Event/Listener/PickupPostType.php:52
msgid "Edit"
msgstr ""

#: src/Components/Event/Listener/PickupPostType.php:53
msgid "Edit Coupon"
msgstr ""

#: src/Components/Event/Listener/PickupPostType.php:54
msgid "New Coupon"
msgstr "Nouveau Coupon"

#: src/Components/Event/Listener/PickupPostType.php:55
msgid "View Pick-ups"
msgstr "Voir ramassages"

#: src/Components/Event/Listener/PickupPostType.php:56
msgid "View Pick-up"
msgstr "Voir ramassage"

#: src/Components/Event/Listener/PickupPostType.php:57
msgid "Search Pick-ups"
msgstr "Rechercher ramassage"

#: src/Components/Event/Listener/PickupPostType.php:58
msgid "No Pick-ups found"
msgstr "Aucun ramassage"

#: src/Components/Event/Listener/PickupPostType.php:59
msgid "No Pick-ups found in trash"
msgstr "Aucun ramassage dans la corbeille"

#: src/Components/Event/Listener/PickupPostType.php:60
msgid "Parent pick-ups"
msgstr "Ramassages"

#: src/Components/Event/Listener/PickupPostType.php:62
msgid "This is where you can add new coupons that customers can use in your store."
msgstr ""

#: src/Components/Event/Listener/PickupPostType.php:90
msgid "Order IDs"
msgstr "Commande IDs"

#: src/Components/Event/Listener/PickupPostType.php:93
msgid "Courier"
msgstr "Courrier"

#: src/Components/Event/Listener/PickupPostType.php:94
msgid "#. Boxes"
msgstr "# de paquet"

#: src/Components/Event/Listener/PickupPostType.php:95
msgid "Weight"
msgstr "Poids"

#: src/Components/Event/Listener/PickupPostType.php:96
msgid "Address"
msgstr "Adresse"

#: src/Components/Event/Listener/PickupPostType.php:97
msgid "Pick-up Time"
msgstr "Heure de ramassage"

#: src/Components/Event/Listener/PickupPostType.php:98
msgid "Status"
msgstr "Status"

#: src/Components/Event/Listener/PickupPostType.php:99
msgid "Actions"
msgstr ""

#: src/Components/Event/Listener/PickupPostType.php:197 src/Components/Event/Listener/PickupPostType.php:199
msgid "Void pick-ups"
msgstr "Annuler le ramassage"

#: src/Components/Event/Listener/PickupPostType.php:200 src/Components/Event/Listener/PickupPostType.php:201
msgid "Reschedule pick-ups"
msgstr "Renouveller le ramassage"

#: src/Components/Event/Listener/PickupPostType.php:234 src/Components/Event/Listener/PickupPostType.php:235
msgid "Schedule pick-up"
msgstr "Nouveau ramassage"

#: src/Components/Event/Listener/PluginPageSettingLink.php:25
msgid "Settings"
msgstr "Paramètres"

#: src/Components/Shipping/Command.php:56
msgid "FlagShip API Error: "
msgstr "FlagShip API Erreur: "

#: src/Components/Shipping/Controller/MetaboxController.php:58
#, php-format
msgid "You have a dispatched FlagShip shipment for this order. FlagShip ID (%s)"
msgstr "Vous avez une expédition FlagShip expédié pour cette commande. FlagShip ID (%s)"

#: src/Components/Shipping/Controller/MetaboxController.php:95
#, php-format
msgid "Unable to access shipment with FlagShip ID (%s)"
msgstr "Incapable d'accéder aux données d'expédition avec FlagShip ID (%s)"

#: src/Components/Shipping/Controller/MetaboxController.php:105
#, php-format
msgid "Unable to void shipment with FlagShip ID (%s)"
msgstr "Incapable d'annuler l'expédition FlagShip avec FlagShip ID (%s)"

#: src/Components/Shipping/Controller/MetaboxController.php:137 src/Components/Shipping/Controller/ShippingController.php:38
msgid "Flagship Shipping has some difficulty in retrieving the rates. Please contact site administrator for assistance.<br/>"
msgstr "FlagShip n'est pas capable de récupérer les couts d'envoi. SVP contactez l'administrateur du site. <br />"

#: src/Components/Shipping/Controller/MetaboxController.php:187
#, php-format
msgid "Unable to schedule pick-up with FlagShip ID (%s)"
msgstr "Incapable d’annuler la demande de ramassage avec FlagShip Ramassage ID (%s)"

#: src/Components/Shipping/Controller/MetaboxController.php:208
#, php-format
msgid "Unable to void pick-up with FlagShip Pickup ID (%s)"
msgstr "Incapable d'annuler l'expédition avec FlagShip ID (%s)"

#: src/Components/Shipping/Controller/ShippingController.php:23
msgid "Add shipping address to get shipping rates! (click \"Calculate Shipping\")"
msgstr "Ajoutez une adresses d'expédition pour voir les taux! ( cliquez \"Calculer les frais d'expédition\")"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:23 src/Components/Shipping/Method/FlagShipWcShippingMethod.php:32 src/Components/Shipping/Method/FlagShipWcShippingMethod.php:63
#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:72 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:16
#: src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:19 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:86
#: src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:95
msgid "FlagShip Shipping"
msgstr "Expédition FlagShip"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:24 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:17
msgid "Obtains real time shipping rates via FlagShip Shipping API"
msgstr "Obtient des tarifs d’expédition en temps réel"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:58 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:81
msgid "Essentials"
msgstr "Général"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:65 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:88
msgid "Enable this shipping method"
msgstr "Activer et utiliser"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:69 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:92
msgid "Method Title"
msgstr "Titre"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:71 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:94
msgid "This controls the name of the shipping service during checkout."
msgstr "Ceci contrôle le nom du service d’expédition au checkout."

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:76 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:99
msgid "FlagShip Access Token"
msgstr "Clé FlagShip"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:284 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:99
msgid "Auto Complete \"Processing\" Orders"
msgstr "Compléter les commandes de \"Processing\" automatiquement"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:285 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:99
msgid "Auto complete \"Processing\" orders when Flagship shipment is confirmed"
msgstr "Compléter les commandes de \"Processing\" automatiquement lorsque l'expédition Flagship est confirmée"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:287 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:99
msgid "If enabled, \"Processing\" order will be automatically set to \"Completed\" when Flagship Shipment is confirmed"
msgstr "Si cette option est activée, les commandes de \"Processing\" sera définie sur \"Completed\" automatiquement lorsque l'expédition Flagship est confirmée"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:78 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:101
msgid "After <a href=\"https://www.flagshipcompany.com/sign-up/\">signup</a>, get a <a target=\"_blank\" href=\"https://auth.smartship.io/tokens/\">access token here</a>."
msgstr "Après vous être <a href=“https://www.flagshipcompany.com/fr/inscrivez-vous/“> inscrit </a>, <a target=\"_blank\" href=\"https://auth.smartship.io/tokens/\">générez une clé FlagShip ici</a>"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:90 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:113
msgid "Offer Standard Rates"
msgstr "Tarifs Standards"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:96 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:119
msgid "Offer Express Rates"
msgstr "Tarifs Express"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:101 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:124
msgid "Offer Overnight Rates"
msgstr "Tarifs Overnight"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:107 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:130
msgid "Offer Rates"
msgstr "Tarifs"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:113 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:136
msgid "Offer the customer all returned rates"
msgstr "Montrer tous les tarifs"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:114 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:137
msgid "Offer the customer the cheapest rate only"
msgstr "Montrer seulement le meilleur tarif"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:115 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:138
msgid "2 cheapest rates"
msgstr "2 meilleurs tarifs"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:116 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:139
msgid "3 cheapest rates"
msgstr "3 meilleurs tarifs"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:117 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:140
msgid "4 cheapest rates"
msgstr "4 meilleurs tarifs"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:118 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:141
msgid "5 cheapest rates"
msgstr "5 meilleurs tarifs"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:122 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:145
msgid "Show fake rate discount in cart/checkout"
msgstr ""

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:128 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:151
msgid "Fake rate discount (%)"
msgstr ""

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:130 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:153
msgid "For instance, 35 stands for 35%"
msgstr ""

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:134 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:157
msgid "Shipper Information"
msgstr "Informations sur l’expéditeur"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:137 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:160
msgid "Shipper information which allows getting live rates, create shipment, schedule pick-up, etc."
msgstr "Information pour l’estimation, la création d’expédition, le ramassage, etc."

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:140 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:163
msgid "Shipper Postal Code"
msgstr "Code postal de l’expéditeur"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:142 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:165
msgid "Enter a valid <strong>Canadian</strong> postal code for the <strong>Shipper</strong>."
msgstr "Entrez un code postal valide pour l’expéditeur."

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:146 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:169
msgid "Shipper City"
msgstr "Ville de l’expéditeur"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:149 src/Components/Shipping/Method/FlagShipWcShippingMethod.php:171 src/Components/Shipping/Method/FlagShipWcShippingMethod.php:186
#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:195 src/Components/Shipping/Method/FlagShipWcShippingMethod.php:207 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:172
#: src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:194 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:209
#: src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:218 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:230
msgid "Required"
msgstr "Requis"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:152 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:175
msgid "Shipper Province"
msgstr "Province de l’expéditeur"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:157 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:180
msgid "Alberta"
msgstr ""

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:158 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:181
msgid "British Columbia"
msgstr "Colombie Britannique"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:159 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:182
msgid "Manitoba"
msgstr ""

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:160 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:183
msgid "New Brunswick"
msgstr "Nouveau Brunswick"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:161 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:184
msgid "NewFoundland & Labrador"
msgstr "Terre Neuve & Labrador"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:162 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:185
msgid "Northwest Territories"
msgstr "Territoires du Nord Ouest"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:163 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:186
msgid "Nova Scotia"
msgstr "Nouvelle Écosse"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:164 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:187
msgid "Nunavut"
msgstr ""

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:165 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:188
msgid "Ontario"
msgstr ""

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:166 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:189
msgid "Prince Edward Island"
msgstr "Île du Prince Edouard"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:167 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:190
msgid "Quebec"
msgstr "Québec"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:168 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:191
msgid "Saskatchwen"
msgstr ""

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:169 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:192
msgid "Yukon"
msgstr ""

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:174 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:197
msgid "Shipper Person Name"
msgstr "Nom d'expéditeur (Attention)"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:177 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:200
msgid "Required, maximum 21 characters"
msgstr "Requis, maximum de 21 caractères"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:183 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:206
msgid "Shipper Company Name"
msgstr "Nom de compagnie d'expéditeur"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:192 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:215
msgid "Shipper Phone Number"
msgstr "Numéro de téléphone d'expéditeur"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:198 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:221
msgid "Shipper Phone Extension Number"
msgstr "Numéro d'extension d'expéditeur"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:201 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:224
msgid "Optional, if applicable"
msgstr "Optionnel"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:204 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:227
msgid "Shipper Street Address"
msgstr "Adresse civile d'expéditeur"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:210 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:233
msgid "Residential"
msgstr "Résidentiel"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:211 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:234
msgid "Shipper Address is Residential?"
msgstr "L'adresse de l’expéditeur est-il résidentiel?"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:216 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:239
msgid "Parcel / Packaging"
msgstr "Pièces"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:218 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:241
msgid "How to split your items into boxes"
msgstr "Comment répartir les produits dans les boites"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:222 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:245
msgid "Box Split"
msgstr "Répartir vos envois dans les boîtes "

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:229
msgid "Everything in one package box"
msgstr "Tous les envois dans la même boîte"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:228
msgid "Split by weight"
msgstr "Divisé en poids"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:230
msgid "One box per item"
msgstr "Une boîte par article"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:231
msgid "Use FlagShip Packing API to pack items into boxes"
msgstr "Utiliser l'API FlagShip pour empaqueter les objets dans les boîtes"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:413
msgid "If the option FlagShip packing API is chosen, you will have to provide at least one package box with dimensions. It will also ignore all settings from the normal weight driven packing method."
msgstr "Si l'option API FlagShip pour empaqueter est choisie, vous allez devoir fournir les dimensions d'au moins une boite. Cela va également ignorer toutes les options reliées à l'estimation par poids."

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:228 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:251
msgid "Box Split Weight"
msgstr "Poids par boîte"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:229 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:252
msgid "Maximun weight per each package box (lbs)"
msgstr "Poids maximal par boîte (livre)"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:240 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:263
msgid "FlagShip Packing"
msgstr "Méthode d'emballage FlagShip"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:430
msgid "Tracking"
msgstr "Suivi par couriel"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:435
msgid "Tracking Emails"
msgstr "Courriels pour les alertes de suivi"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:438
msgid "(optional) If provided, the email address(s) will always receive tracking notifications. Emails should be semicolon separated (;)."
msgstr "(optionnel) Si elles sont fournies, les adresses électroniques recevront toujours des notifications de suivi. Les courriels doivent être séparés par des points-virgules (;)."

#: src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:264
msgid "Allow FlagShip to pack the order's products, given sets of package box dimension"
msgstr "Fournir une solution d'emballage"

#: src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:266
msgid "By enabling this packing method, you will have to provide at least one Package Box dimensions. It will also ignore all settings from the normal weight driven packing method."
msgstr "Vous devez avoir au moins un type de boîte pour utiliser la méthode d'emballage FlagShip."

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:250 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:273
msgid "Tax"
msgstr ""

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:255 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:278
msgid "Calculate tax"
msgstr "Calcul de Tax"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:256 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:279
msgid "Click here to include taxes in the price. Only use this if WooCommerce is not applying taxes to your cart"
msgstr "Cliquez ici pour inclure les taxes dans le prix. Utilisez cette option seulement si WooCommerce n’applique pas les taxes au panier."

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:258 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:281
msgid "If you have taxes enabled, make sure you don’t click this box or you will double tax the shipping fees."
msgstr "Si vous avez les taxes d’activées, assurez vous de ne pas cocher cette boite ou vous appliquerez les taxes deux fois sur les couts d’envoi."

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:262 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:285
msgid "Markup"
msgstr "Marge Commerciale"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:264 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:287
msgid "Store owner may apply additional fee for shipping."
msgstr "L’expéditeur peut choisir d’appliquer une marge sur l’expédition."

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:268 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:291
msgid "Shipping Cost Markup Type"
msgstr "Type de marge sur le cout"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:269 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:292
msgid "Shipping Cost Markup Type can be either flat rate (i.e. dollar valued) or percentage rate (i.e. rate based on certain percentage)"
msgstr "Le type de marge sur l’expédition peut être montant fixe (en $) ou un taux (en %)"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:273 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:296
msgid "Flat rate"
msgstr "Montant fixe"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:274 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:297
msgid "Percentage"
msgstr "Pourcentage"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:279 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:302
msgid "Shipping Cost Markup"
msgstr "Marge commerciale d'expédition"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:284 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:307
msgid "Pickup"
msgstr "Ramassage"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:286 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:309
msgid "schedule pick-up for your shipment. Don't forget to attach labels!"
msgstr "Planifier un ramassage. Préparez et attachez toutes les étiquettes."

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:290 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:313
msgid "Pick-up Start Time"
msgstr "Ramassage à partir de"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:307 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:330
msgid "Pick-up End Time"
msgstr "Ramassage jusqu'à"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:324 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:347
msgid "Contact Email"
msgstr "Courriel"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:334 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:357
msgid "Disable FedEx Rates"
msgstr "Désactiver FedEx"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:340 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:363
msgid "Disable UPS Rates"
msgstr "Désactiver UPS"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:345 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:368
msgid "Disable Purolator Rates"
msgstr "Désactiver Purolator"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:345 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:368
msgid "Disable Canpar Rates"
msgstr "Désactiver Canpar"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:345 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:368
msgid "Disable Canada Post Rates"
msgstr "Désactiver Canada Post"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:351 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:374
msgid "Disable Cart/Checkout API warning"
msgstr "Cacher les messages d’erreur"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:354 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:377
#, fuzzy
msgid "Once disabled, FlagShip will store warnings under following option \"Cart/Checkout API warning logs\""
msgstr "Une fois cachés, les messages d’erreur seront affichés dans la section suivante"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:359 src/Components/Shipping/Method/Legacy_Flagship_WC_Shipping_Method.php:382
msgid "Cart/Checkout API warning logs (10 latest)"
msgstr "Messages d’erreur (10 récents)"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:555
msgid "Signature required on delivery"
msgstr "Signature requise à la livraison"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:539
msgid "Show transit time in shopping cart"
msgstr "Afficher le temps de transit dans le panier"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:540
msgid "If checked, the transit times of couriers will be shown"
msgstr "Si coché, les temps de transit des coursiers seront affichés"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php
msgid "Send tracking updates to billing email"
msgstr "Envoyer des mises à jour de suivi au email de facturation"

#: src/Components/Shipping/Method/FlagShipWcShippingMethod.php:556
msgid "If checked, all the shipments to this shipping zone will be signature required on delivery"
msgstr "Si coché, tous les envois vers cette zone d'expédition seront signés à la livraison"

#: src/Components/Shipping/Request/Builder/Cart/PackageItems/FallbackBuilder.php:28
#, php-format
msgid "Product %s is missing weight, weight default to 1 lbs."
msgstr "Le poids est manquant pour le produit %s, poids utilisé par défaut est de 1 livre."

#: src/Components/Shipping/Request/Builder/Order/PackageItems/ApiBuilder.php:29
msgid "Unable to use FlagShip Packing API. Use fallback weight driven packing."
msgstr ""

#: src/Components/Validation/AddressEssentialValidator.php:24
msgid "Address corrected to match with shipper's postal code."
msgstr "L'adresse a été changée pour correspondre au code postal."

#: src/Components/Validation/IntegrityValidator.php:54
msgid "<strong>Shipping Integrity Failure:</strong> <br/>"
msgstr "<strong>Problème d'intégrité de la configuration:</strong> <br/>"

#: src/Components/Validation/PhoneValidator.php:14
#, php-format
msgid "\"%s\" is not a valid phone number."
msgstr "“%s” n’est pas un numéro de téléphone valide."

#: src/Components/Validation/SettingsValidator.php:20
msgid "FlagShip Shipping is disabled."
msgstr "FlagShip n'est pas activé."

#: src/Components/Validation/SettingsValidator.php:42
msgid "Shipper person name is missing."
msgstr "Nom de l’expéditeur est manquant."

#: src/Components/Validation/SettingsValidator.php:46
msgid "Shipper company name is missing."
msgstr "Nom de compagnie de l’expéditeur est manquant."

#: src/Components/Validation/SettingsValidator.php:50
msgid "Shipper phone number is missing."
msgstr "Numéro de téléphone de l’expéditeur est manquant."

#: src/Components/Validation/SettingsValidator.php:54
msgid "Shipper address's streetline is missing."
msgstr "Numéro d’adresse de l’expéditeur est manquant."

#: templates/meta-boxes/order-flagship-shipping-actions.php:6
msgid "Summary"
msgstr "Sommaire"

#: templates/meta-boxes/order-flagship-shipping-actions.php:13
msgid "Service"
msgstr ""

#: templates/meta-boxes/order-flagship-shipping-actions.php:17
msgid "Tracking Number"
msgstr "Numéro de suivi"

#: templates/meta-boxes/order-flagship-shipping-actions.php:21
msgid "Cost"
msgstr "Côut"

#: templates/meta-boxes/order-flagship-shipping-actions.php:25
msgid "Print labels"
msgstr "Imprimer les étiquettes"

#: templates/meta-boxes/order-flagship-shipping-actions.php:28
msgid "Regular label"
msgstr "Étiquette standard"

#: templates/meta-boxes/order-flagship-shipping-actions.php:29 templates/meta-boxes/order-flagship-shipping-actions.php:33 templates/meta-boxes/order-flagship-shipping-actions.php:38
msgid "Print"
msgstr "Imprimer"

#: templates/meta-boxes/order-flagship-shipping-actions.php:32
msgid "Thermal label"
msgstr "Étiquette thermale"

#: templates/meta-boxes/order-flagship-shipping-actions.php:37
msgid "Commercial invoice"
msgstr "Facture commerciale"

#: templates/meta-boxes/order-flagship-shipping-actions.php:46
msgid "Confirmation ID"
msgstr "Numéro de confirmation"

#: templates/meta-boxes/order-flagship-shipping-actions.php:48
msgid "Date"
msgstr ""

#: templates/meta-boxes/order-flagship-shipping-actions.php:50
msgid "Void pick-up"
msgstr "Annuler le ramassage"

#: templates/meta-boxes/order-flagship-shipping-actions.php:53
msgid "Request for pick-up"
msgstr "Demande de ramassage"

#: templates/meta-boxes/order-flagship-shipping-actions.php:55
msgid "Schedule"
msgstr "Demander"

#: templates/meta-boxes/order-flagship-shipping-actions.php:59
msgid "Cancel Shipment"
msgstr "Annuler cette expédition"

#: templates/meta-boxes/order-flagship-shipping-actions.php:59
msgid "use with caution"
msgstr "attention"

#: templates/meta-boxes/order-flagship-shipping-actions.php:60
msgid "Void Shipment"
msgstr "Annuler cette expédition"

#: templates/meta-boxes/order-flagship-shipping-actions.php:63
msgid "This order has already been exported to FlagShip:"
msgstr "Cette commande a déjà été exportée vers FlagShip"

#: templates/meta-boxes/order-flagship-shipping-actions.php:64
msgid "Client Choosen Rate"
msgstr "Service choisi par le client"

#: templates/meta-boxes/order-flagship-shipping-actions.php:77
msgid "Requote Rates"
msgstr "Estimation récente"

#: templates/meta-boxes/order-flagship-shipping-actions.php:88
msgid "Options"
msgstr ""

#: templates/meta-boxes/order-flagship-shipping-actions.php:95
msgid "Shipping Date (Optional, default today):"
msgstr "Date d'expédition (optionnel, aujourd'hui par défaut):"

#: templates/meta-boxes/order-flagship-shipping-actions.php:102
msgid "Enable Insurance"
msgstr "Assurer mon expédition"

#: templates/meta-boxes/order-flagship-shipping-actions.php:108
msgid "Insured items' value (Required):"
msgstr "Valeur totale des produits (Requis):"

#: templates/meta-boxes/order-flagship-shipping-actions.php:111
msgid "<br/>$ value of the items to insure. Note that exlusions apply, see <a href=\"https://www.flagshipcompany.com/terms-and-conditions\" target=\"_blank\">here</a> for details"
msgstr "<br/>$ en valeur à assurer. Certaines exclusions s'appliquent. Consultez <a href=\"https://www.flagshipcompany.com/fr/modalites-et-conditions/\" target=\"_blank\">ici</a> pour savoir plus"

#: templates/meta-boxes/order-flagship-shipping-actions.php:117
msgid "Description (Required):"
msgstr "Description (Requis):"

#: templates/meta-boxes/order-flagship-shipping-actions.php:125
msgid "<abbr title=\"cash on delivery\">Enable COD</abbr>"
msgstr "<abbr title=\"cash on delivery\">PSL(COD)</abbr>"

#: templates/meta-boxes/order-flagship-shipping-actions.php:143
msgid "Payable to (Required):"
msgstr "Payé à (Requis):"

#: templates/meta-boxes/order-flagship-shipping-actions.php:150
msgid "Receiver Phone (Required):"
msgstr "Téléphone (Requis):"

#: templates/meta-boxes/order-flagship-shipping-actions.php:157
msgid "Amount (Required):"
msgstr "Montant (Requis):"

#: templates/meta-boxes/order-flagship-shipping-actions.php:157
msgid "Export to FlagShip"
msgstr "Exporter vers FlagShip"

#: templates/meta-boxes/order-flagship-shipping-actions.php:177
msgid "Signature Required"
msgstr "Signature requise"

#: templates/meta-boxes/order-flagship-shipping-actions.php:183
msgid "Reference (Optional):"
msgstr "Référence (Optionnel):"

#: templates/meta-boxes/order-flagship-shipping-actions.php:188
msgid "Driver Instruction (Optional):"
msgstr "Instructions pour le livreur (Optionnel):"

#: templates/meta-boxes/order-flagship-shipping-actions.php:145
msgid "Tracking emails (Optional):"
msgstr "Suivi par couriel (Optionnel):"

#: templates/meta-boxes/order-flagship-shipping-actions.php:193 templates/meta-boxes/order-flagship-shipping-actions.php:236
msgid "Create shipment"
msgstr "Créer l'expédition"

#: templates/meta-boxes/order-flagship-shipping-actions.php:194 templates/meta-boxes/order-flagship-shipping-actions.php:237
msgid "Requote"
msgstr "Reéstimer"

#: templates/meta-boxes/order-flagship-shipping-actions.php:225
msgid "Shipment was not quoted with FlagShip Shipping."
msgstr "Le commande n'a pas encore eu l'estimation."

#: templates/meta-boxes/order-flagship-shipping-actions.php:228
msgid "Latest rates"
msgstr "Estimation récente"

#: templates/meta-boxes/order-flagship-shipping-actions.php:239
msgid "Get a quote"
msgstr "Avoir une estimation"

#: templates/notice.php:4
msgid "Set your FlagShip Shipping token."
msgstr "Configurer votre clé (token) FlagShip."

#: templates/option/log.php:15
msgid "Timestamp"
msgstr "Date"

#: templates/option/log.php:16
msgid "Log"
msgstr "Message"

#: templates/option/package-box.php:2
msgid "Package Box"
msgstr "Boîte"

#: templates/option/package-box.php:9
msgid "Model Name"
msgstr "Modèle"

#: templates/option/package-box.php:10
msgid "Length (in)"
msgstr "LO (po)"

#: templates/option/package-box.php:11
msgid "Width (in)"
msgstr "LA (po)"

#: templates/option/package-box.php:12
msgid "Height (in)"
msgstr "H (po)"

#: templates/option/package-box.php:13
msgid "Weight (LB)"
msgstr "Poids (LBS)"

#: templates/option/package-box.php
msgid "Outer"
msgstr "Extérieure"

#: templates/option/package-box.php
msgid "Inner"
msgstr "Intérieur"

#: templates/option/package-box.php
msgid "Supported"
msgstr "Soutenu"

#: templates/option/package-box.php
msgid "Empty"
msgstr "Vide"

#: templates/option/package-box.php
msgid "Markup ($)"
msgstr "Marge ($)"

#: templates/option/package-box.php
msgid "Supported weight is the maximum supported weight. Empty weight is the weight of an empty box."
msgstr "Le poids soutenus est le poids maximum soutenus. Le poids vide est le poids d'une boîte vide."

#: templates/option/package-box.php
msgid "(optional) Shipping Rate Adjustment ($)"
msgstr "(facultatif) Ajustement du Taux d'expédition ($)"

#: templates/option/package-box.php:41
msgid "+ Add package box"
msgstr "Ajouter une boîte"

#: templates/option/package-box.php:44
msgid "Remove selected package box(es)"
msgstr "Enlever une(des) boîte(s)"

#: templates/option/package-box.php
msgid "If set, an additional fee will be charged on each package using this model of box"
msgstr "Si défini, des frais supplémentaires seront facturés sur chaque colis utilisant ce modèle de boîte"

#: src/Components/Validation/IntegrityValidator.php:
msgid "Address integrity test failure"
msgstr "Échec du test d'intégrité d'adresse"

#: src/Components/Validation/IntegrityValidator.php:
msgid "Invalid API token"
msgstr "Clef d'API FlagShip invalide"

#: src/Components/Shipping/RateProcessor/NativeRateProcessor.php:
msgid "(Time in Transit %s)"
msgstr "(Temps de Transit %s)"

msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d jour"
msgstr[1] "%d jours"

#, fuzzy
#~| msgid " is not a valid phone number."
#~ msgid "\"%s\" is not a valid Fqsen."
#~ msgstr "n'est pas un numéro de téléphone valide."

#, fuzzy
#~| msgid "Residential"
#~ msgid "identical(%s)"
#~ msgstr "Résidentiel"

#~ msgid "Unable to retrieve the latest quote."
#~ msgstr "FlagShip n'est pas capable de récupérer les couts d'envoi."

#~ msgid "Unable to access shipment with FlagShip ID"
#~ msgstr "Incapable d'accéder aux données d'expédition avec FlagShip ID"

#~ msgid "Unable to void shipment with FlagShip ID"
#~ msgstr "Incapable d'annuler l'expédition avec FlagShip ID"

#~ msgid "Unable to void pick-up with FlagShip Pickup ID"
#~ msgstr "Incapable d’annuler la demande de ramassage avec FlagShip Ramassage ID"

#~ msgid "Unable to schedule pick-up with FlagShip ID"
#~ msgstr "Incapable d’effectuer le ramassage avec FlagShip ID"

#~ msgid "Smartship Access Token"
#~ msgstr "Clé FlagShip"

#~ msgid "Help to break down shipping products into parcel boxes."
#~ msgstr "Permettez-vous de rendre vos produits dans moindre boites possible"

#~ msgid "Enter valid <strong>Canadian</strong> postcode for the <strong>Shipper</strong>."
#~ msgstr "Entrez-vous une code postale valide Canadienne"

#~ msgid "A few of shipper information which allows getting live rates, create shipment, schedule pick-up, etc."
#~ msgstr "Information par rapport avec estimation, création d’expédition, ramassage, etc."

#~ msgid "Shipper Criteria"
#~ msgstr "Info. de l’expéditeur"

#~ msgid "After <a href=\"https://smartship-ng.flagshipcompany.com/company/register\">signup</a>, get a <a target=\"_blank\" href=\"https://auth.smartship.io/tokens/\">access token here</a>."
#~ msgstr ""
#~ "Après <a href=\"https://smartship-ng.flagshipcompany.com/company/register\">vous-inscrivez un compte</a>, et par la suite d'obtenir une <a target=\"_blank\" href=\"https://auth.smartship.io/tokens/"
#~ "\">clé secrète d'accès ici</a>."

#~ msgid "This controls the title which the user sees during checkout."
#~ msgstr "Ce-ci apparaît devant les clients en tant le titre d'expéditeur."

#, fuzzy
#~ msgid "Enable FlagShip Shipping"
#~ msgstr "Activer FlagShip Expédition"
