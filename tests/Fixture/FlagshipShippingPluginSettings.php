<?php

return array(
    'enabled' => 'yes',
    'title' => 'FlagShip Shipping',
    'token' => 'fake_token',
    'allow_standard_rates' => 'yes',
    'allow_express_rates' => 'yes',
    'allow_overnight_rates' => 'yes',
    'offer_rates' => 'all',
    'origin' => 'H9R5P9',
    'freight_shipper_city' => 'POINTE-CLAIRE',
    'freight_shipper_state' => 'QC',
    'shipper_person_name' => 'FlagShip Tester',
    'shipper_company_name' => 'FlagShip WooCommerce Test App',
    'shipper_phone_number' => '****** 320 8383',
    'shipper_phone_ext' => '',
    'freight_shipper_street' => '148 Brunswick',
    'default_package_box_split' => 'no',
    'default_package_box_split_weight' => '20',
    'apply_tax_by_flagship' => 'no',
    'default_shipping_markup_type' => 'percentage',
    'default_shipping_markup' => '10.00',
    'default_pickup_time_from' => '09:00',
    'default_pickup_time_to' => '17:00',
    'default_shipping_email' => '<EMAIL>',
    'disable_courier_fedex' => 'no',
    'disable_courier_ups' => 'no',
    'disable_courier_purolator' => 'no',
    'disable_api_warning' => 'no',
    'api_warning_log' => '',
);
