=== FlagShip WooCommerce Shipping  ===
Contributors: flagshipit
Tags: WooCommerce, shipping, ecommerce, e-commerce, courier, commerce
Requires at least: 4.6
Tested up to: 6.4.2
WC tested up to: 8.4.0
Stable tag: 3.0.30
Requires PHP: 5.6
License: GNU Public License 3.0
License URI: https://www.gnu.org/licenses/gpl-3.0.en.html

FlagShip WooCommerce Shipping is an e-shipping courier solution that helps you shipping anything from Canada. Beautifully.

== Description ==

FlagShip WooCommerce Shipping plugin allows WooCommerce based stores to have the same convenient way to ship with FlagShip as on the FlagShip website. This plugin will display shipping rates on the shopping cart and checkout page. It also allows getting shipping rates in the wordpress admin site and creating a shipment. It can also enable the seller and the shopper to receive updates on a shipment. With a long list of parameters configurable, sellers can easily customize how shipping rates are displayed on store. Additionally, the order information can be exported to FlagShip website and all the shipping can be handled on the FlagShip website.

== Installation ==

= system requirements =
- minimum:

Requires PHP v5.6+ WordPress v4.6+ WooCommerce v3.0+. For WooCommerce installation guide, please visit [woocommerce installation guide](https://docs.woothemes.com/document/installing-uninstalling-woocommerce/).

= non-technical requirements =
- Owning an activated **FlagShip Account** (If not, you can sign up for one at: https://www.flagshipcompany.com/sign-up/)
- Having a **FlagShip Shipping API** access token. ([click here](https://auth.smartship.io/tokens/) to request an access token!)

= Before starting, make sure you have properly done all of the following steps: =
- Owning a WordPress site with administrator access
- Having **WooCommerce** Plugin installed, activated, and configured

= Automatic installation =
- Go to your WordPress dashborad, navigate to the plugins menu, and "Add new". In the add plugins page, type "FlagShip WooCommerce Shipping" in the search field and you will find us. By clicking "install now", the plugin will be automatically installed on your WordPress site.

= Manual installation =
- Download Latest Release [Here](https://github.com/flagshipcompany/flagship-for-woocommerce/releases/latest)
- Log in as admin on your WordPress website. Go to the dashboard. Then, go to plugins page (on left hand side navigation submenu).
- Click on "Add New" and then "Upload plugin". Select the file you just downloaded (zip file) and upload it to the site.

= After installation =
- In the plugins page click "Activate" of the FlagShip WooCommerce Shipping plugin
- Click "Settings" of the FlagShip WooCommerce Shipping plugin
- In the settings, make sure the checkbox "Enable this shipping method" is checked
- Take some time to fill out all of the required or applicable settings. (Access Token is absolutely required. Otherwise, You won't be able to get discounted rate nor being able to print the shipping labels.)
- In the section `Parcel/Packaging`, there are four options to choose from for the packing of items into package boxes. If you choose FlagShip packing, you need to provide boxes with dimensions.
- Then head to shipping zones to configure the settings for shipping. Go to WooCommerce Settings > Shipping > Shipping Zones and edit each shipping zone that needs the shipping settings to be configured. You can add and enable the FlagShip WooCommerce Shipping method. You also need to edit the FlagShip WooCommerce Shipping method of each shipping zone to save all the shipping settings for the shipping zone.

== Frequently Asked Questions ==

Technical support will be provided at: <EMAIL>, **************

== Screenshots ==

- https://user-images.githubusercontent.com/8826928/28284208-2cfeda16-6afe-11e7-9c01-871484d1e7ee.png
- https://user-images.githubusercontent.com/8826928/28284244-4c3f4c1c-6afe-11e7-85c1-e2f913d9b4e8.png
- https://cloud.githubusercontent.com/assets/5373898/19041682/fa13c97e-8956-11e6-8907-df3f6728c1d1.png
- https://user-images.githubusercontent.com/8826928/28284546-4fb67d6a-6aff-11e7-8281-737a4bb29ed9.png
== Changelog ==

== Upgrade Notice ==