{"authors": [{"email": "<EMAIL>", "name": "yi-FLS"}], "autoload": {"psr-4": {"FS\\": "src/"}}, "autoload-dev": {"psr-4": {"FS\\Test\\": "tests/"}}, "description": "An e-shipping courier solution that helps you shipping anything from Canada.", "license": "GPL-3.0-or-later", "minimum-stability": "stable", "name": "flagshipcompany/flagship-for-woocommerce", "require": {"php": ">=5.4"}, "scripts": {"test": ["phpunit"], "test:prep": ["bash bin/install-wp-tests.sh wordpress_test root '' localhost"]}, "type": "wordpress-plugin", "require-dev": {"phpunit/phpunit": "5.7"}}