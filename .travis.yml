language: php

branches:
  only:
    - master
    - /^rfc.*$/
    - /^v(\d+\.)?(\d+\.)?(\*|\d+|.)$/

services:
  - mysql

php:
  - 5.6
  - 7.0
  - 7.1
  - 7.2
  - 7.3
  - 7.4

env:
  - WP_VERSION=latest WP_MULTISITE=0

matrix:
  include:
    - php: 5.6
      env: WP_VERSION=4.8 WP_MULTISITE=0
    - php: 5.6
      env: WP_VERSION=4.7 WP_MULTISITE=0
    - php: 5.6
      env: WP_VERSION=4.6 WP_MULTISITE=0
    - php: 5.6
      env: WP_VERSION=4.5 WP_MULTISITE=0
    - php: 5.6
      env: WP_VERSION=4.4 WP_MULTISITE=0
    - php: 5.6
      env: WP_VERSION=4.3 WP_MULTISITE=0
    - php: 5.6
      env: WP_VERSION=4.2 WP_MULTISITE=0

before_script:
  # - git clone https://github.com/woothemes/woocommerce ../woocommerce
  - wget https://github.com/woocommerce/woocommerce/archive/3.0.3.tar.gz && tar xf 3.0.3.tar.gz && cp -R woocommerce-3.0.3 ../woocommerce && rm -R woocommerce-3.0.3
  # - wget https://github.com/woothemes/woocommerce/archive/2.5.5.tar.gz && tar xf 2.5.5.tar.gz && cp -R woocommerce-2.5.5 ../woocommerce && rm -R woocommerce-2.5.5
  - bash bin/install-wp-tests.sh wordpress_test root '' localhost $WP_VERSION
  - composer install

script: composer test

notifications:
  slack:
    rooms:
      - secure: p40g8q3gS4YDdnz24sMOMOZrVxLYoz4V07IAu5PWriRui98oJya37Ur/T6rbkMilO0vFHLvE0uDLx059yioC0FAm7WhEbRxFMaLHJex2sDvY3UvjzsL2K055Xa+jpn5dtiBCRXv9VyhY2XZLlqQEN/rFwQmCdbr+psnEX59+DBQjkU2PjCxehfM6A+OpgOrtviHP9YpYPRMI4FBEYn6dez3vcH+P+NcGHZ08pRurFKuy8oXzLDmRUOgXys+eD8tg5g+CjKp7ROqXpGJYPIQtJqI7T+jK1jdw+lW4ZmUDqd1XPt4elpMZYKwp4cS4z+CmDzzRlXjQYoQS2J3MRP4u97GmOrDilbMEXfqerTRvqrrolk6J0qDEBPBSKzR7cLCQqwdpUFTg3rvKU3/9t+m25ARwFYVTMJqA+GUxNVO6P/lGbklL2FVKOZ37S6x71yPm4ynIoYvu3TxQGMcHqEntyGgFRU5qYZvxwFU5QG2X7FqiALkqn1tXcQI5r0/nAVzUC3GC8oGrNvFmTlDB3/99KD26nZOqAfKyqWiqG+QG39e+xxHEacpxi1KQvaqd04/ruITqlW1vAIWR7/fdfjdRyHCnA6slenjb/IE+x3m5Wg7M1zFuoyTGe7df62B1q3FPDdV2xuWtsZxZDrit3obIbHU/Lcuzk34v/sQsHTRgJpA=